       # ZbInnovation - Pre-launch Website PRD

## Project Overview

### Purpose
The ZbInnovation pre-launch website serves as a landing platform to generate interest and collect early adopters for an upcoming innovation platform. The platform's mission is to bridge the gap between startups, innovators, and supporting organizations within Zimbabwe's growing innovation ecosystem.

### Target Audience
- Startup founders and entrepreneurs
- Innovation hub managers
- Investors and venture capitalists
- Technology enthusiasts
- Corporate innovation teams
- Academic institutions

## Technical Specifications

### Core Technologies
- Vue 3 (Composition API) with TypeScript
- Quasar Framework v2.x for UI components
- Pinia for state management
- Vue Router for navigation
- SCSS for custom styling
- Vite for build tooling

### Required NPM Packages
```json
{
  "dependencies": {
    "@quasar/extras": "^1.16.x",
    "quasar": "^2.12.x",
    "vue": "^3.3.x",
    "vue-router": "^4.2.x",
    "pinia": "^2.1.x",
    "flip-countdown": "^1.2.x",
    "typescript": "^5.0.x",
    "sass": "^1.32.x",
    "axios": "^1.4.x",
    "@vueuse/core": "^10.x",
    "date-fns": "^2.30.x"
  }
}
```

## Brand Guidelines

### Color Palette
```scss
// Primary Colors
$primary: #0D8A3E;      // Main brand green
$secondary: #dfefe6;     // Light green background
$accent: #F5A623;       // Call-to-action orange

// Neutral Colors
$dark: #333333;         // Text color
$grey: #666666;         // Secondary text
$light-grey: #F5F5F5;   // Background grey

// Status Colors
$success: #4CAF50;
$warning: #FFC107;
$error: #FF5252;
$info: #2196F3;
```

### Typography
```scss
// Font Families
$primary-font: 'Inter', rubik;
$heading-font: 'Poppins', rubik;

// Font Sizes
$h1-size: 3rem;
$h2-size: 2.5rem;
$h3-size: 2rem;
$h4-size: 1.5rem;
$body-size: 1rem;
$small-size: 0.875rem;
```

## Detailed Component Specifications

### 1. Main Layout (`MainLayout.vue`)

#### Header Component
```scss
.header {
  background: $secondary;
  height: 70px;
  padding: 0 24px;
  
  .logo {
    height: 35px;
    @media (max-width: 767px) {
      height: 30px;
    }
  }
  
  .brand-text {
    font-family: $heading-font;
    font-weight: 700;
    font-size: 1.2rem;
    color: $primary;
    margin-left: 12px;
  }
}
```

#### Social Media Icons
```scss
.social-icons {
  .q-btn {
    margin: 0 8px;
    color: $primary;
    
    &:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease;
    }
    
    .q-icon {
      font-size: 24px;
    }
  }
}
```

#### Navigation Menu
```scss
.nav-menu {
  .q-btn {
    font-weight: 500;
    padding: 8px 16px;
    
    &:hover {
      background: rgba($primary, 0.1);
    }
  }
}
```

### 2. Landing Page (`Home.vue`)

#### Hero Section
```scss
.hero-section {
  height: 80vh;
  background-image: url('/images/hero-bg.jpg');
  background-size: cover;
  background-position: center;
  
  .overlay {
    background: linear-gradient(
      rgba(0, 0, 0, 0.5),
      rgba(0, 0, 0, 0.7)
    );
  }
  
  .content {
    max-width: 800px;
    text-align: center;
    color: white;
    
    h1 {
      font-size: 4rem;
      font-weight: 700;
      margin-bottom: 24px;
      
      @media (max-width: 767px) {
        font-size: 2.5rem;
      }
    }
  }
}
```

#### Countdown Timer Configuration
```typescript
interface CountdownConfig {
  theme: 'dark' | 'light';
  labels: {
    days: 'Days';
    hours: 'Hours';
    minutes: 'Minutes';
    seconds: 'Seconds';
  };
  styles: {
    container: {
      backgroundColor: '#ffffff';
      padding: '20px';
      borderRadius: '10px';
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)';
    };
    flipCard: {
      backgroundColor: '#0D8A3E';
      color: '#ffffff';
      fontSize: '2rem';
      padding: '20px';
    };
  };
}
```

#### Features Grid
```scss
.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 48px 24px;
  
  @media (max-width: 1023px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
  }
  
  .feature-card {
    padding: 24px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}
```

### 3. News Page (`News.vue`)

#### Article Grid Layout
```scss
.news-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  
  @media (max-width: 1023px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
  }
}
```

#### Article Card Component
```typescript
interface ArticleCard {
  image: string;
  title: string;
  excerpt: string;
  date: string;
  category: string;
  author: {
    name: string;
    avatar: string;
  };
}

const cardStyles = {
  borderRadius: '8px',
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  
  image: {
    height: '200px',
    objectFit: 'cover',
  },
  
  content: {
    padding: '16px',
  },
  
  category: {
    color: '#0D8A3E',
    fontWeight: '500',
    fontSize: '0.875rem',
  },
}
```

### 4. Single Article Page (`ArticleView.vue`)

#### Article Header
```scss
.article-header {
  position: relative;
  height: 400px;
  
  .featured-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .overlay {
    background: linear-gradient(
      transparent,
      rgba(0, 0, 0, 0.7)
    );
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 48px 24px;
    color: white;
  }
}
```

#### Content Layout
```scss
.article-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 24px;
  
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 48px;
  
  @media (max-width: 1023px) {
    grid-template-columns: 1fr;
  }
  
  .main-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: $dark;
    
    h2 {
      font-size: 2rem;
      margin: 32px 0 16px;
    }
    
    p {
      margin-bottom: 24px;
    }
  }
  
  .sidebar {
    position: sticky;
    top: 24px;
  }
}
```

### 5. Dashboard (`Dashboard.vue`)

#### Layout Structure
```scss
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  
  .welcome-section {
    background: $secondary;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 24px;
    
    h1 {
      color: $primary;
      margin-bottom: 16px;
    }
  }
}
```

#### Email Confirmation Banner
```typescript
interface EmailBanner {
  state: 'unconfirmed' | 'pending' | 'confirmed';
  messages: {
    unconfirmed: string;
    pending: string;
    confirmed: string;
  };
  actions: {
    resend: () => Promise<void>;
    dismiss: () => void;
  };
}

const bannerStyles = {
  unconfirmed: {
    background: '#FFF3E0',
    border: '1px solid #FFB74D',
    color: '#E65100',
  },
  confirmed: {
    background: '#E8F5E9',
    border: '1px solid #81C784',
    color: '#2E7D32',
  },
}
```

## User Journey Specifications

### 1. Newsletter Signup Flow
```mermaid
graph TD
    A[User Enters Email] --> B{Validate Email}
    B -->|Valid| C[Send Confirmation]
    B -->|Invalid| D[Show Error]
    C --> E[Show Success Message]
    E --> F[Send Welcome Email]
```

### 2. Email Templates

#### Confirmation Email
```html
Subject: Confirm your email - ZbInnovation

<div style="max-width: 600px; margin: 0 auto; padding: 24px;">
  <h1>Welcome to ZbInnovation</h1>
  <p>Please confirm your email to stay updated with our latest news and announcements.</p>
  <a href="{confirmation_link}" style="
    display: inline-block;
    background: #0D8A3E;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    text-decoration: none;
  ">
    Confirm Email
  </a>
</div>
```

#### Welcome Email
```html
Subject: Welcome to the Innovation Community!

<div style="max-width: 600px; margin: 0 auto; padding: 24px;">
  <h1>Welcome to ZbInnovation</h1>
  <p>Thank you for joining our community! Here's what you can expect:</p>
  <ul>
    <li>Weekly updates on innovation trends</li>
    <li>Early access to platform features</li>
    <li>Exclusive event invitations</li>
  </ul>
</div>
```

## Animation Specifications

### 1. Page Transitions
```scss
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}
```

### 2. Component Animations
```scss
// Card Hover Effects
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Button Hover Effects
.btn-hover {
  transition: background-color 0.2s ease, transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}
```

## Content Guidelines

### Article Structure
```typescript
interface ArticleContent {
  title: string;          // Max 60 characters
  excerpt: string;        // 120-150 characters
  content: string;        // Rich text content
  coverImage: {
    url: string;
    alt: string;
    caption?: string;
  };
  category: string;
  tags: string[];
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: Date;
}
```

### SEO Requirements
```typescript
interface SEOMetadata {
  title: string;          // Max 60 characters
  description: string;    // Max 155 characters
  keywords: string[];
  ogImage: string;
  twitterCard: string;
}
```

## Performance Requirements

### Loading Times
- First Contentful Paint: < 1.5s
- Time to Interactive: < 3.5s
- Largest Contentful Paint: < 2.5s

### Image Optimization
- Max image size: 800KB
- Formats: WebP with JPEG fallback
- Lazy loading for images below the fold

## Browser Support
- Chrome (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- Edge (latest 2 versions)

## Accessibility Requirements
- WCAG 2.1 Level AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Proper ARIA labels
- Color contrast ratios: 4.5:1 minimum

## Error Handling

### Form Validation Messages
```typescript
const validationMessages = {
  email: {
    required: 'Please enter your email address',
    invalid: 'Please enter a valid email address',
    exists: 'This email is already registered'
  },
  general: {
    error: 'Something went wrong. Please try again.',
    success: 'Operation completed successfully',
    loading: 'Please wait...'
  }
}
```

### API Error Handling
```typescript
interface APIError {
  status: number;
  message: string;
  code: string;
  details?: Record<string, any>;
}

const errorHandling = {
  400: 'Invalid request. Please check your input.',
  401: 'Please log in to continue.',
  403: 'You don\'t have permission to access this resource.',
  404: 'The requested resource was not found.',
  500: 'Server error. Please try again later.'
}
``` 
