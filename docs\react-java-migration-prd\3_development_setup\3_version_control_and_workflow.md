# 3. Version Control and Workflow

## 🔄 **Version Control Strategy**

The ZbInnovation platform uses Git with a structured branching strategy designed for team collaboration, continuous integration, and reliable releases. This document outlines the complete version control workflow and best practices.

## 🌳 **Branching Strategy**

### **GitFlow Branching Model**
```
main (production)
├── develop (integration)
│   ├── feature/JIRA-123-user-profile-creation
│   ├── feature/JIRA-124-dashboard-implementation
│   └── feature/JIRA-125-ai-integration
├── release/v1.2.0
├── hotfix/JIRA-456-security-patch
└── support/v1.1.x
```

### **Branch Types and Purposes**

#### **Main Branches**
**`main`** (Production Branch):
- Contains production-ready code
- Protected branch with strict merge requirements
- Automatic deployment to production environment
- Tagged with version numbers for releases

**`develop`** (Integration Branch):
- Integration branch for feature development
- Contains latest development changes
- Automatic deployment to staging environment
- Base branch for feature branches

#### **Supporting Branches**
**`feature/*`** (Feature Branches):
- Naming: `feature/JIRA-{ticket-number}-{brief-description}`
- Created from: `develop`
- Merged back to: `develop`
- Lifetime: Until feature is complete and merged

**`release/*`** (Release Branches):
- Naming: `release/v{major}.{minor}.{patch}`
- Created from: `develop`
- Merged to: `main` and `develop`
- Purpose: Release preparation and bug fixes

**`hotfix/*`** (Hotfix Branches):
- Naming: `hotfix/JIRA-{ticket-number}-{brief-description}`
- Created from: `main`
- Merged to: `main` and `develop`
- Purpose: Critical production fixes

## 📝 **Commit Standards**

### **Commit Message Format**
```
type(scope): brief description

Detailed description of the change, including:
- What was changed and why
- Any breaking changes
- Related issues or tickets

JIRA: PROJ-123
Co-authored-by: Developer Name <<EMAIL>>
```

### **Commit Types**
```typescript
interface CommitType {
  feat: 'New feature implementation';
  fix: 'Bug fix';
  docs: 'Documentation changes';
  style: 'Code style changes (formatting, etc.)';
  refactor: 'Code refactoring without functionality changes';
  test: 'Adding or updating tests';
  chore: 'Maintenance tasks, dependency updates';
  perf: 'Performance improvements';
  ci: 'CI/CD pipeline changes';
  build: 'Build system changes';
  revert: 'Reverting previous changes';
}
```

### **Commit Message Examples**
```bash
# Feature implementation
feat(auth): implement JWT-based authentication system

Add comprehensive JWT authentication with refresh tokens,
including login, logout, and token refresh endpoints.
Includes rate limiting and security measures.

JIRA: PROJ-123

# Bug fix
fix(dashboard): resolve profile completion calculation error

Fix calculation logic that was showing incorrect completion
percentages for users with partial profile data.

JIRA: PROJ-456

# Documentation update
docs(api): update authentication endpoint documentation

Add detailed examples and error response codes for all
authentication endpoints in the API documentation.

JIRA: PROJ-789
```

## 🔀 **Workflow Processes**

### **Feature Development Workflow**
```bash
# 1. Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/JIRA-123-user-profile-creation

# 2. Develop feature with regular commits
git add .
git commit -m "feat(profile): add user profile creation form"

# 3. Keep feature branch updated
git checkout develop
git pull origin develop
git checkout feature/JIRA-123-user-profile-creation
git rebase develop

# 4. Push feature branch
git push origin feature/JIRA-123-user-profile-creation

# 5. Create pull request for code review
# 6. Merge to develop after approval
```

### **Release Workflow**
```bash
# 1. Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v1.2.0

# 2. Update version numbers and changelog
npm version 1.2.0
git add .
git commit -m "chore(release): bump version to 1.2.0"

# 3. Final testing and bug fixes
git commit -m "fix(release): resolve minor UI issues"

# 4. Merge to main and develop
git checkout main
git merge --no-ff release/v1.2.0
git tag -a v1.2.0 -m "Release version 1.2.0"

git checkout develop
git merge --no-ff release/v1.2.0

# 5. Delete release branch
git branch -d release/v1.2.0
git push origin --delete release/v1.2.0
```

### **Hotfix Workflow**
```bash
# 1. Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/JIRA-456-security-patch

# 2. Implement critical fix
git add .
git commit -m "fix(security): patch authentication vulnerability"

# 3. Merge to main and develop
git checkout main
git merge --no-ff hotfix/JIRA-456-security-patch
git tag -a v1.2.1 -m "Hotfix version 1.2.1"

git checkout develop
git merge --no-ff hotfix/JIRA-456-security-patch

# 4. Deploy immediately to production
```

## 🛡️ **Branch Protection Rules**

### **Main Branch Protection**
```yaml
# GitHub branch protection settings
main:
  required_status_checks:
    strict: true
    contexts:
      - "ci/backend-tests"
      - "ci/frontend-tests"
      - "ci/integration-tests"
      - "ci/security-scan"
  enforce_admins: true
  required_pull_request_reviews:
    required_approving_review_count: 2
    dismiss_stale_reviews: true
    require_code_owner_reviews: true
  restrictions:
    users: []
    teams: ["senior-developers", "tech-leads"]
```

### **Develop Branch Protection**
```yaml
develop:
  required_status_checks:
    strict: true
    contexts:
      - "ci/backend-tests"
      - "ci/frontend-tests"
      - "ci/lint-check"
  required_pull_request_reviews:
    required_approving_review_count: 1
    dismiss_stale_reviews: true
```

## 🔍 **Code Review Process**

### **Pull Request Template**
```markdown
## Description
Brief description of changes made in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance impact assessed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Code is commented where necessary
- [ ] Documentation updated
- [ ] No new warnings introduced

## JIRA Ticket
PROJ-123

## Screenshots (if applicable)
[Add screenshots for UI changes]

## Additional Notes
Any additional information for reviewers.
```

### **Code Review Guidelines**
**Review Criteria**:
- **Functionality**: Code works as intended and meets requirements
- **Code Quality**: Clean, readable, and maintainable code
- **Testing**: Adequate test coverage and quality
- **Performance**: No obvious performance issues
- **Security**: No security vulnerabilities
- **Documentation**: Proper documentation and comments

**Review Process**:
1. **Automated Checks**: All CI/CD checks must pass
2. **Peer Review**: At least one team member review required
3. **Senior Review**: Senior developer review for complex changes
4. **Approval**: All reviewers must approve before merge
5. **Merge**: Squash and merge to maintain clean history

## 🤖 **Automated Workflows**

### **GitHub Actions Workflows**
```yaml
# .github/workflows/ci.yml
name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
      - name: Run backend tests
        run: ./mvnw test
      - name: Generate test report
        uses: dorny/test-reporter@v1
        if: success() || failure()
        with:
          name: Backend Tests
          path: target/surefire-reports/*.xml
          reporter: java-junit

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run frontend tests
        run: npm run test:coverage
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
```

### **Pre-commit Hooks**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/eslint/eslint
    rev: v8.28.0
    hooks:
      - id: eslint
        files: \.(js|ts|tsx)$
        types: [file]
```

## 📊 **Git Workflow Metrics**

### **Development Metrics**
- **Lead Time**: Time from feature start to production deployment
- **Cycle Time**: Time from first commit to merge
- **Deployment Frequency**: How often code is deployed to production
- **Change Failure Rate**: Percentage of deployments causing failures
- **Mean Time to Recovery**: Time to recover from failures

### **Code Quality Metrics**
- **Code Review Coverage**: Percentage of code changes reviewed
- **Review Turnaround Time**: Time from PR creation to approval
- **Defect Escape Rate**: Bugs found in production vs. development
- **Technical Debt**: Accumulated technical debt over time

## 🔧 **Git Configuration**

### **Global Git Configuration**
```bash
# User configuration
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Editor and merge tool
git config --global core.editor "code --wait"
git config --global merge.tool "vscode"

# Line ending configuration
git config --global core.autocrlf input  # Linux/Mac
git config --global core.autocrlf true   # Windows

# Push configuration
git config --global push.default simple
git config --global pull.rebase true

# Alias configuration
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
```

### **Repository-specific Configuration**
```bash
# .gitconfig (repository level)
[core]
    hooksPath = .githooks
    
[branch "main"]
    remote = origin
    merge = refs/heads/main
    
[branch "develop"]
    remote = origin
    merge = refs/heads/develop
```

## 📚 **Git Best Practices**

### **Commit Best Practices**
- **Atomic Commits**: Each commit should represent a single logical change
- **Descriptive Messages**: Clear, concise commit messages
- **Regular Commits**: Commit frequently to avoid large changesets
- **Clean History**: Use interactive rebase to clean up commit history

### **Branch Management**
- **Short-lived Branches**: Keep feature branches small and short-lived
- **Regular Updates**: Keep branches updated with latest develop changes
- **Clean Merges**: Use squash and merge for feature branches
- **Delete Merged Branches**: Clean up merged branches promptly

### **Collaboration Guidelines**
- **Communication**: Discuss significant changes with team
- **Documentation**: Update documentation with code changes
- **Testing**: Ensure all tests pass before pushing
- **Code Review**: Participate actively in code reviews

---

## 📚 **Reference Documents**

**Coding Standards**: See `/3_development_setup/2_coding_standards_and_guidelines.md`
**CI/CD Pipeline**: See `/3_development_setup/4_ci_cd_pipeline_configuration.md`
**Team Collaboration**: See `/3_development_setup/5_team_collaboration_tools.md`
**JIRA Integration**: See `/development-standards/JIRA_PROJECT_STRUCTURE.md`

*This version control workflow ensures efficient, reliable, and collaborative development for the ZbInnovation platform.*
